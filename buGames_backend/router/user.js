const express = require('express')

const router = express.Router()

const { login } = require('../router_handler/user')
// const {  register } = require('../router_handler/user')

const expressJoi = require('@escook/express-joi')
const userSchema = require('../schema/user')

router.post('/login', expressJoi(userSchema), login)
// router.post('/register', expressJoi(userSchema), register)

module.exports = router
