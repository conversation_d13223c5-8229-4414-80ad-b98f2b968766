const bcrypt = require('bcryptjs')

const login = (req, res) => {
  const userinfo = req.body
  if(!userinfo.username || !userinfo.password){
    return res.cc('用户名或密码不能为空')
  }
  const db = require('../db')
  const sql = 'select * from user where username = ?'
  db.query(sql, userinfo.username, (err, results) => {
    if(err) return res.cc(err)
    if(results.length !== 1) return res.cc('用户名或密码错误')
    if(!bcrypt.compareSync(userinfo.password, results[0].password)) return res.cc('用户名或密码错误')

    const user = { ...results[0], password: '' }
    const jwt = require('jsonwebtoken')
    const config = require('../config')
    const token = jwt.sign(user, config.jwtSecretKey, { expiresIn: config.expiresIn })
    
    res.send({
      status: 0,
      message: '登录成功', 
      token: 'Bearer ' + token
    })
  })
}

// const register = (req, res) => {
//   const userinfo = req.body 
//   if(!userinfo.username || !userinfo.password){
//     return res.cc('用户名或密码不能为空')
//   }
//   userinfo.password = bcrypt.hashSync(userinfo.password, 10)
//   const db = require('../db')
//   const sql = 'insert into user (username, password) values (?, ?)'
//   db.query(sql, [userinfo.username, userinfo.password], (err, results) => {
//     if(err) return res.cc(err)
//     if(results.affectedRows !== 1) return res.cc('注册失败')
//     res.send({status: 0, message: '注册成功'})
//   })
// }

module.exports = {
  login,
  // register
}
